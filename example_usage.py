from langchain_core.messages import HumanMessage
from catchup.graph import graph

async def chat_example():
    """Example of using the chatbot with enhanced state structure."""
    
    session_id = "user_123_session_456"
    config = {
        "configurable": {
            "thread_id": session_id,
            "model_name": "anthropic/claude-3.5-sonnet"
        }
    }
    
    # Use enhanced state structure
    inputs1 = {
        "messages": [HumanMessage(content="Hi, I'm looking for a restaurant")],
        "user_context": {
            "user_id": "user_123",
            "email_address": "<EMAIL>",
            "location": {
                "latitude": 45.4666,
                "longitude": 9.1832
            },
            "current_session_start": "2024-01-15T10:30:00"
        },
        "session_config": {
            "session_id": session_id,
            "memory_budget": 15,
            "max_tool_calls_per_turn": 3,
            "conversation_timeout": 30,
            "preferred_response_style": "conversational",
            "enable_proactive_suggestions": True
        },
        "current_phase": "GREETING",
        "conversation_metrics": {
            "message_count": 1,
            "tool_calls_count": 0,
            "successful_tool_calls": 0,
            "failed_tool_calls": 0,
            "conversation_start": "2024-01-15T10:30:00",
            "last_activity": "2024-01-15T10:30:00",
            "estimated_tokens_used": 0,
            "user_satisfaction_indicators": []
        }
    }
    
    result1 = await graph.ainvoke(inputs1, config)
    print("Bot:", result1["messages"][-1].content)
    
    # Follow-up interaction (remembers context)
    inputs2 = {
        "messages": [HumanMessage(content="Something Italian would be great")],
        "session_id": session_id,
        "memory_lenght": "15"
    }
    
    result2 = await graph.ainvoke(inputs2, config)
    print("Bot:", result2["messages"][-1].content)
    
    # Change memory limit mid-conversation
    inputs3 = {
        "messages": [HumanMessage(content="What did I ask for initially?")],
        "session_id": session_id,
        "memory_lenght": "5"  # Reduce to 5 messages
    }
    
    result3 = await graph.ainvoke(inputs3, config)
    print("Bot:", result3["messages"][-1].content)
