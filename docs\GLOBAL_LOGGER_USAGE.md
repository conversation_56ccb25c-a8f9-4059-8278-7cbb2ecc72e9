# Global Logger Usage Guide

This guide explains how to use the global logger system to eliminate the need for importing logging configuration in every file.

## Quick Start

### Method 1: Global Logger (Recommended)

The simplest way to add logging to any file:

```python
from shared.logger import logger

def my_function():
    logger.info("This is an info message")
    logger.error("This is an error message")
    logger.debug("This is a debug message")
```

### Method 2: Import from Shared Module

```python
from shared import logger

def my_function():
    logger.info("Using logger from shared module")
```

## Configuration

The global logger uses a **hierarchical configuration system** with the following priority:

### 1. **Config File** (Highest Priority)
If `logging.conf` exists in the project root, it will be used:
```ini
[LOG_SETTINGS]
LOG_LEVEL = INFO
CONSOLE_LOGGING = true
FILE_LOGGING = true
LOG_DIR = logs
MAX_FILE_SIZE_MB = 10
BACKUP_COUNT = 5
```

### 2. **Environment Variables** (Medium Priority)
If no config file exists, environment variables are used:
   - `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
   - `LOG_FILE`: Custom log file name
   - `LOG_DIR`: Log directory (default: logs)
   - `CONSOLE_LOGGING`: Enable console logging (default: true)
   - `FILE_LOGGING`: Enable file logging (default: true)

### 3. **Default Configuration** (Lowest Priority)
If neither config file nor environment variables exist:
   - Log level: INFO
   - Console logging: Enabled
   - File logging: Enabled with rotation

## Migration from Old Approach

### Before (old approach):
```python
from shared.logger import logger



def my_function():
    logger.info("Message")
```

### After (new approach):
```python
from shared.logger import logger

def my_function():
    logger.info("Message")
```

## When to Use Module-Specific Loggers

For better traceability in large applications, you might still want module-specific loggers:

```python
from shared.logger import logger

  # Will show module name in logs

def my_function():
    logger.info("This will show the module name")
```

## Advanced Configuration

If you need custom logging configuration, you can still use the traditional approach:

```python
from shared.logging_config import setup_logging, get_logger

# Custom setup
setup_logging(
    log_level="DEBUG",
    console_logging=True,
    log_file="custom.log"
)


```

## Configuration Options

### Config File (logging.conf)
Create a `logging.conf` file in your project root:
```ini
[LOG_SETTINGS]
LOG_LEVEL = DEBUG
CONSOLE_LOGGING = true
FILE_LOGGING = true
LOG_DIR = logs
LOG_FILE = my_app.log
MAX_FILE_SIZE_MB = 10
BACKUP_COUNT = 5
```

### Environment Variables
Set these environment variables to configure logging:
```bash
# Logging level
export LOG_LEVEL=DEBUG

# Enable/disable console output
export CONSOLE_LOGGING=true

# Enable/disable file logging
export FILE_LOGGING=true

# Custom log directory
export LOG_DIR=./logs

# Custom log file name
export LOG_FILE=my_app.log
```

### File System Logging Control
You can completely disable file logging while keeping console logging:
- **Config file**: Set `FILE_LOGGING = false`
- **Environment**: Set `FILE_LOGGING=false`
- **Code**: Call `setup_logging(file_logging=False)`

## Benefits of Global Logger

1. **Simplicity**: One import line instead of two
2. **Consistency**: Same logger configuration across all modules
3. **Automatic setup**: No need to remember to configure logging
4. **Environment-aware**: Automatically picks up environment configuration
5. **Backward compatible**: Old approach still works

## File Structure

```
src/
├── shared/
│   ├── __init__.py          # Exports logger
│   ├── logger.py            # Global logger module
│   └── logging_config.py    # Logging configuration functions
└── your_module.py           # Your code using: from shared.logger import logger
```

## Examples

See `examples/logging_usage.py` for complete examples of all logging approaches.
