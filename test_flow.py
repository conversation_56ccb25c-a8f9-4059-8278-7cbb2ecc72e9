#!/usr/bin/env python3
"""Test the complete flow: call_model → conditional_edge_1 → mcp_tools → call_model."""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from catchup.state import CatchUpState
from catchup.graph import conditional_edge_1


def test_flow_logic():
    """Test the flow logic with different message scenarios."""
    print("Testing the flow logic...\n")
    
    # Scenario 1: User asks a question that requires tools
    print("1. User asks: 'What categories are available?'")
    print("   → call_model processes this and decides to call get_categories tool")
    
    # Simulate AI response with tool call
    ai_response_with_tool = AIMessage(
        content="I'll help you find the available categories.",
        tool_calls=[
            {
                "name": "get_categories", 
                "args": {},
                "id": "call_abc123"
            }
        ]
    )
    
    state_with_tool_call = CatchUpState(messages=[ai_response_with_tool])
    edge_decision = conditional_edge_1(state_with_tool_call)
    print(f"   → conditional_edge_1 decision: {edge_decision}")
    print("   → Flow goes to mcp_tools to execute get_categories")
    
    # Scenario 2: After tool execution, results come back
    print("\n2. Tool execution returns results")
    tool_result = ToolMessage(
        content='{"categories": ["Restaurant", "Beauty", "Fitness"]}',
        tool_call_id="call_abc123"
    )
    
    print("   → mcp_tools executes get_categories and returns results")
    print("   → Flow goes back to call_model with tool results")
    
    # Scenario 3: call_model processes tool results and gives final answer
    print("\n3. call_model processes tool results")
    final_ai_response = AIMessage(
        content="Here are the available categories: Restaurant, Beauty, and Fitness. Which one interests you?"
    )
    
    state_final = CatchUpState(messages=[final_ai_response])
    final_decision = conditional_edge_1(state_final)
    print(f"   → conditional_edge_1 decision: {final_decision}")
    print("   → Flow ends as no more tools needed")
    
    print("\n" + "="*60)
    print("COMPLETE FLOW SUMMARY:")
    print("1. START → call_model (user question)")
    print("2. call_model → conditional_edge_1 (AI decides to use tools)")
    print("3. conditional_edge_1 → mcp_tools (execute tools)")
    print("4. mcp_tools → call_model (return results)")
    print("5. call_model → conditional_edge_1 (AI gives final answer)")
    print("6. conditional_edge_1 → END (no more tools needed)")


def test_multiple_tool_calls():
    """Test scenario with multiple tool calls."""
    print("\n" + "="*60)
    print("TESTING MULTIPLE TOOL CALLS SCENARIO")
    print("="*60)
    
    print("\nScenario: User asks 'I want to book a restaurant deal'")
    print("This might require multiple tool calls:")
    
    # First tool call: get categories
    print("\n1. First call_model decides to get categories")
    ai_msg1 = AIMessage(
        content="Let me find restaurant categories for you.",
        tool_calls=[{"name": "get_categories", "args": {}, "id": "call_1"}]
    )
    decision1 = conditional_edge_1(CatchUpState(messages=[ai_msg1]))
    print(f"   Decision: {decision1} → Execute get_categories")
    
    # After tool execution, back to call_model
    print("\n2. After getting categories, call_model decides to search deals")
    ai_msg2 = AIMessage(
        content="Now let me search for restaurant deals.",
        tool_calls=[{"name": "search_deals", "args": {"category": "Restaurant"}, "id": "call_2"}]
    )
    decision2 = conditional_edge_1(CatchUpState(messages=[ai_msg2]))
    print(f"   Decision: {decision2} → Execute search_deals")
    
    # Final response
    print("\n3. After getting deals, call_model provides final answer")
    ai_msg3 = AIMessage(
        content="Here are the available restaurant deals: [deal list]. Which one would you like to book?"
    )
    decision3 = conditional_edge_1(CatchUpState(messages=[ai_msg3]))
    print(f"   Decision: {decision3} → End conversation")
    
    print("\nThis shows how the loop allows for multiple tool calls as needed!")


if __name__ == "__main__":
    test_flow_logic()
    test_multiple_tool_calls()
    
    print("\n" + "="*60)
    print("✓ Flow logic test completed successfully!")
    print("The implementation correctly supports:")
    print("  - Single tool calls")
    print("  - Multiple sequential tool calls") 
    print("  - Proper routing between call_model and mcp_tools")
    print("  - Ending when no more tools are needed")
