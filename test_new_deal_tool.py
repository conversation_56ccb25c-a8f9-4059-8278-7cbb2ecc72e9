#!/usr/bin/env python3
"""Test script to verify the new get_deals_by_category_excluding_user tool."""

import asyncio
import sys
import os

# Add src to path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from catchup.tools.get_deals_by_categoryId import get_deals_by_categoryId
from catchup.Models.model import Deal


def test_tool_import():
    """Test that the tool can be imported successfully."""
    print("Testing tool import...")
    
    try:
        # Test that the tool function exists
        assert callable(get_deals_by_categoryId)
        print("✓ Tool imported successfully")
        
        # Test that the tool has the expected attributes
        assert hasattr(get_deals_by_categoryId, 'name')
        assert hasattr(get_deals_by_categoryId, 'description')
        print(f"✓ Tool name: {get_deals_by_categoryId.name}")
        print(f"✓ Tool description: {get_deals_by_categoryId.description[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Tool import failed: {e}")
        return False


def test_tool_in_loader():
    """Test that the tool is properly loaded in the tools loader."""
    print("\nTesting tool in loader...")
    
    try:
        from catchup.tools_loader import get_all_catchup_tools
        print("✓ Tools loader imported successfully")
        
        # Note: We can't actually call get_all_catchup_tools() here because it's async
        # and requires MCP server connection, but we can verify the import works
        return True
        
    except Exception as e:
        print(f"✗ Tools loader test failed: {e}")
        return False


def test_deal_model():
    """Test that the Deal model works correctly."""
    print("\nTesting Deal model...")
    
    try:
        # Test creating a Deal instance
        test_deal = Deal(
            id="test-deal-123",
            title="Test Deal",
            description="A test deal for validation",
            category_id="cat-123",
            owner_id="owner-456",
            price=29.99,
            discount_percentage=20.0,
            business_name="Test Business",
            location="Test Location",
            availability="Available"
        )
        
        assert test_deal.id == "test-deal-123"
        assert test_deal.title == "Test Deal"
        assert test_deal.price == 29.99
        print("✓ Deal model works correctly")
        
        # Test with minimal data (optional fields)
        minimal_deal = Deal(
            id="minimal-deal",
            title=None,
            description=None,
            category_id=None,
            owner_id=None,
            price=None,
            discount_percentage=None,
            business_name=None,
            location=None,
            availability=None
        )
        
        assert minimal_deal.id == "minimal-deal"
        assert minimal_deal.title is None
        print("✓ Deal model handles optional fields correctly")
        
        return True
        
    except Exception as e:
        print(f"✗ Deal model test failed: {e}")
        return False


def test_tool_signature():
    """Test that the tool has the correct signature."""
    print("\nTesting tool signature...")
    
    try:
        import inspect
        
        # Get the function signature
        sig = inspect.signature(get_deals_by_categoryId.func)
        params = list(sig.parameters.keys())
        
        # Check expected parameters
        expected_params = ['category_id', 'user_id']
        for param in expected_params:
            assert param in params, f"Missing parameter: {param}"
        
        print(f"✓ Tool has correct parameters: {params}")
        
        # Check return type annotation
        return_annotation = sig.return_annotation
        print(f"✓ Return type annotation: {return_annotation}")
        
        return True
        
    except Exception as e:
        print(f"✗ Tool signature test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("Testing new get_deals_by_category_excluding_user tool")
    print("=" * 60)
    
    tests = [
        test_tool_import,
        test_tool_in_loader,
        test_deal_model,
        test_tool_signature,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The new tool is ready to use.")
        print("\nNext steps:")
        print("1. Test with actual Supabase data (requires database connection)")
        print("2. Test integration with the LangGraph workflow")
        print("3. Verify the tool works in the complete customer service system")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
