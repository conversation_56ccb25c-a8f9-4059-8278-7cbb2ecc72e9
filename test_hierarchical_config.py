#!/usr/bin/env python3
"""
Test script for hierarchical logging configuration.
Tests: Config file > Environment variables > Defaults
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add src to path so we can import from shared
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def cleanup_logging():
    """Reset logging configuration for clean tests."""
    import logging
    import importlib

    # Clear all handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Reset the global configuration flag
    import shared.logging_config as lc
    lc._is_configured = False
    lc._global_logger = None

    # Reload modules to ensure clean state
    importlib.reload(lc)
    if 'shared.logger' in sys.modules:
        importlib.reload(sys.modules['shared.logger'])

def test_config_file_priority():
    """Test that config file takes priority over environment variables."""
    print("\n=== Test 1: Config File Priority ===")
    
    # Set environment variables
    os.environ['LOG_LEVEL'] = 'ERROR'
    os.environ['CONSOLE_LOGGING'] = 'false'
    os.environ['FILE_LOGGING'] = 'false'
    
    # Create a config file with different settings
    config_content = """[LOG_SETTINGS]
LOG_LEVEL = DEBUG
CONSOLE_LOGGING = true
FILE_LOGGING = false
LOG_DIR = test_logs
"""
    
    with open('logging.conf', 'w') as f:
        f.write(config_content)
    
    try:
        cleanup_logging()
        from shared.logger import logger
        
        # Test that config file settings are used (not environment)
        logger.debug("This debug message should appear (config file LOG_LEVEL=DEBUG)")
        logger.info("Config file priority test completed")
        
        print("✓ Config file priority test passed")
        return True
        
    except Exception as e:
        print(f"✗ Config file priority test failed: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists('logging.conf'):
            os.remove('logging.conf')
        if os.path.exists('test_logs'):
            shutil.rmtree('test_logs')

def test_environment_variables():
    """Test environment variable configuration when no config file exists."""
    print("\n=== Test 2: Environment Variables ===")
    
    # Ensure no config file exists
    if os.path.exists('logging.conf'):
        os.remove('logging.conf')
    
    # Set environment variables
    os.environ['LOG_LEVEL'] = 'WARNING'
    os.environ['CONSOLE_LOGGING'] = 'true'
    os.environ['FILE_LOGGING'] = 'false'
    os.environ['LOG_DIR'] = 'env_logs'
    
    try:
        cleanup_logging()
        from shared.logger import logger
        
        logger.debug("This debug message should NOT appear (LOG_LEVEL=WARNING)")
        logger.warning("This warning message should appear")
        logger.info("Environment variables test completed")
        
        print("✓ Environment variables test passed")
        return True
        
    except Exception as e:
        print(f"✗ Environment variables test failed: {e}")
        return False
    finally:
        # Cleanup environment
        for key in ['LOG_LEVEL', 'CONSOLE_LOGGING', 'FILE_LOGGING', 'LOG_DIR']:
            if key in os.environ:
                del os.environ[key]
        if os.path.exists('env_logs'):
            shutil.rmtree('env_logs')

def test_default_configuration():
    """Test default configuration when no config file or environment variables exist."""
    print("\n=== Test 3: Default Configuration ===")
    
    # Ensure no config file exists
    if os.path.exists('logging.conf'):
        os.remove('logging.conf')
    
    # Clear environment variables
    for key in ['LOG_LEVEL', 'CONSOLE_LOGGING', 'FILE_LOGGING', 'LOG_DIR', 'LOG_FILE']:
        if key in os.environ:
            del os.environ[key]
    
    try:
        cleanup_logging()
        from shared.logger import logger
        
        logger.info("This info message should appear (default LOG_LEVEL=INFO)")
        logger.debug("This debug message should NOT appear (default LOG_LEVEL=INFO)")
        logger.warning("Default configuration test completed")
        
        print("✓ Default configuration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Default configuration test failed: {e}")
        return False

def test_file_logging_toggle():
    """Test file logging can be disabled while keeping console logging."""
    print("\n=== Test 4: File Logging Toggle ===")

    # Clean up any existing logs first
    if os.path.exists('logs'):
        shutil.rmtree('logs')

    # Test with environment variable
    os.environ['FILE_LOGGING'] = 'false'
    os.environ['CONSOLE_LOGGING'] = 'true'
    os.environ['LOG_LEVEL'] = 'INFO'

    try:
        cleanup_logging()
        from shared.logger import logger

        logger.info("This should only appear in console, not in any file")

        # Check that no NEW log files were created after our test
        logs_created = os.path.exists('logs') and any(Path('logs').glob('*.log'))
        if logs_created:
            print("✗ File logging toggle test failed: Log files were created when FILE_LOGGING=false")
            return False

        print("✓ File logging toggle test passed")
        return True

    except Exception as e:
        print(f"✗ File logging toggle test failed: {e}")
        return False
    finally:
        # Cleanup
        for key in ['FILE_LOGGING', 'CONSOLE_LOGGING', 'LOG_LEVEL']:
            if key in os.environ:
                del os.environ[key]

def main():
    """Run all hierarchical configuration tests."""
    print("=== Hierarchical Logging Configuration Test Suite ===")
    
    tests = [
        test_config_file_priority,
        test_environment_variables,
        test_default_configuration,
        test_file_logging_toggle
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n=== Test Results ===")
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All hierarchical configuration tests passed!")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
