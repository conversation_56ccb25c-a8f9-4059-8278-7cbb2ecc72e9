#!/usr/bin/env python3
"""
Test script for the global logger functionality.
"""

import os
import sys

# Add src to path so we can import from shared
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_global_logger():
    """Test the global logger import and usage."""
    print("Testing global logger...")
    
    try:
        # Test Method 1: Direct import
        from shared.logger import logger
        logger.info("✓ Global logger import successful")
        logger.debug("✓ Debug message from global logger")
        logger.warning("✓ Warning message from global logger")
        
        # Test Method 2: Import from shared module
        from shared import logger as shared_logger
        shared_logger.info("✓ Shared module logger import successful")
        
        print("✓ All global logger tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Global logger test failed: {e}")
        return False

def test_traditional_logger():
    """Test the traditional logger approach still works."""
    print("\nTesting traditional logger...")
    
    try:
        from shared.logger import logger
        traditional_
        traditional_logger.info("✓ Traditional logger still works")
        
        print("✓ Traditional logger test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Traditional logger test failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Global Logger Test Suite ===")
    
    # Set environment variable for testing
    os.environ['CONSOLE_LOGGING'] = 'true'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    success1 = test_global_logger()
    success2 = test_traditional_logger()
    
    if success1 and success2:
        print("\n🎉 All tests passed! Global logger is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
