"""
Example showing how to migrate from old logging approach to global logger.

This file shows before/after examples of migrating existing code.
"""

# ============================================================================
# BEFORE: Old approach (what you currently have in many files)
# ============================================================================

# OLD CODE:
"""
from shared.logger import logger



def some_function():
    logger.info("Processing data...")
    logger.debug("Debug information")
    logger.error("An error occurred")
"""

# ============================================================================
# AFTER: New approach with global logger
# ============================================================================

# NEW CODE (Method 1 - Recommended):
from shared.logger import logger

def some_function():
    logger.info("Processing data...")
    logger.debug("Debug information")
    logger.error("An error occurred")

# NEW CODE (Method 2 - Alternative):
from shared import logger

def another_function():
    logger.info("Processing more data...")
    logger.warning("A warning message")

# ============================================================================
# Migration Steps:
# ============================================================================

"""
To migrate an existing file:

1. Replace this line:
   from shared.logger import logger
   
   With this line:
   from shared.logger import logger

2. Remove this line:
   

3. Keep all your existing logger.info(), logger.error(), etc. calls unchanged

That's it! Your logging will work exactly the same but with less boilerplate.
"""

# ============================================================================
# Real Example: Migrating src/catchup/supabase/client.py
# ============================================================================

"""
BEFORE (current code):
```python
from shared.logger import logger



# ... rest of the file uses logger.info(), etc.
```

AFTER (migrated code):
```python
from shared.logger import logger

# ... rest of the file uses logger.info(), etc. (unchanged)
```

You save 1 line and 1 import!
"""

# ============================================================================
# When to keep the old approach
# ============================================================================

"""
Keep the old approach when:

1. You need module-specific logger names in your log output
2. You're working on a library that shouldn't assume global configuration
3. You need different logging configurations for different modules

Example where you might keep the old approach:
```python
from shared.logger import logger

# This will show "myapp.database.client" in log messages

```

vs global logger which shows "global" in log messages.
"""

if __name__ == "__main__":
    print("=== Migration Example ===")
    some_function()
    another_function()
    print("Migration example completed successfully!")
