#!/usr/bin/env python3
"""Test script to verify MCP server connection and tool loading."""

import asyncio
import sys
import os

# Add src to path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from shared.mcp_tools import get_catchup_tools, get_mcp_tools_manager


async def test_mcp_connection():
    """Test the MCP server connection and tool loading."""
    print("Testing MCP server connection...")
    print("Server URL: https://genenrativepangea.app.n8n.cloud/mcp/catchup/sse")
    
    try:
        # Test getting the manager
        print("\n1. Getting MCP tools manager...")
        manager = await get_mcp_tools_manager()
        print(f"✓ Manager created: {type(manager).__name__}")
        
        # Test loading tools
        print("\n2. Loading tools from MCP server...")
        tools = await get_catchup_tools()
        print(f"✓ Loaded {len(tools)} tools")
        
        # Display tool information
        if tools:
            print("\n3. Available tools:")
            for i, tool in enumerate(tools, 1):
                print(f"   {i}. {tool.name}")
                print(f"      Description: {tool.description}")
                if hasattr(tool, 'args_schema') and tool.args_schema:
                    try:
                        if hasattr(tool.args_schema, 'schema'):
                            print(f"      Schema: {tool.args_schema.schema()}")
                        else:
                            print(f"      Schema: {tool.args_schema}")
                    except Exception as e:
                        print(f"      Schema: <error getting schema: {e}>")
                print()
        else:
            print("\n3. No tools loaded - this might indicate:")
            print("   - MCP server is not available")
            print("   - Network connectivity issues")
            print("   - Authentication problems")
            print("   - Server configuration issues")
        
        print("✓ MCP connection test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Error testing MCP connection: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False


async def test_graph_with_tools():
    """Test the graph with MCP tools integration."""
    print("\n" + "="*50)
    print("Testing graph with MCP tools...")
    
    try:
        from catchup.graph import graph
        from langchain_core.messages import HumanMessage
        
        # Test input
        inputs = {
            "messages": [HumanMessage(content="Hello, what can you help me with?")],
            "user_id": "test_user_123",
            "email_address": "<EMAIL>",
            "latitude": "45.4642",
            "longitude": "9.1900"
        }
        
        config = {
            "configurable": {
                "model_name": "anthropic/claude-3.5-sonnet",
                "system_prompt": "You are a helpful customer service assistant."
            }
        }
        
        print("Invoking graph...")
        result = await graph.ainvoke(inputs, config)
        
        print(f"✓ Graph execution completed")
        print(f"✓ Result contains {len(result.get('messages', []))} messages")
        
        # Display the response
        messages = result.get('messages', [])
        if messages:
            last_message = messages[-1]
            print(f"\nLast message type: {type(last_message).__name__}")
            if hasattr(last_message, 'content'):
                print(f"Content preview: {str(last_message.content)[:200]}...")
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                print(f"Tool calls: {len(last_message.tool_calls)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing graph: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("CatchUp MCP Integration Test")
    print("="*50)
    
    # Test 1: MCP connection
    mcp_success = await test_mcp_connection()
    
    # Test 2: Graph integration (only if MCP works)
    if mcp_success:
        graph_success = await test_graph_with_tools()
    else:
        print("\nSkipping graph test due to MCP connection failure")
        graph_success = False
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    print(f"MCP Connection: {'✓ PASS' if mcp_success else '✗ FAIL'}")
    print(f"Graph Integration: {'✓ PASS' if graph_success else '✗ FAIL'}")
    
    if mcp_success and graph_success:
        print("\n🎉 All tests passed! MCP integration is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    asyncio.run(main())
