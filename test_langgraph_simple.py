#!/usr/bin/env python3
"""Simple test of LangGraph with fixed parameters."""

import asyncio
from langchain_core.messages import HumanMessage
from src.catchup.graph import graph
import random

async def test_langgraph_simple():
    """Test LangGraph with a simple fixed input and display the response."""

    print("🚀 LangGraph Simple Test")
    print("=" * 40)
    memory_session_id = str(random.choice(range(100))) + "-" + str(random.choice(range(100))) + "-" + str(random.choice(range(100))) + "-" + str(random.choice(range(100)))
    # Fixed test parameters
    session_id = memory_session_id
    test_message = "Hello! What can you help me with?"
    test_message ="Cerco per i prossimi giorni, un aperitivo"
    # Configuration for the graph
    config = {
        "configurable": {
            "thread_id": session_id,
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful assistant."
        }
    }

    # Initial state with fixed parameters
    initial_state = {
        "messages": [HumanMessage(content=test_message)],
        "session_id": session_id,
        "user_id":"fe95e629-0a4e-474b-97d1-fafe9d6863e3",
        "memory_lenght": "5",
        "latitude": "45.4666",
        "longitude": "9.1832"
    }

    print(f"📝 Input: {test_message}")
    print(" Invoking graph...")

    try:
        # Invoke the graph
        result = await graph.ainvoke(initial_state)

        # Display the response
        if result and "messages" in result and result["messages"]:
            response = result["messages"][-1].content
            print("\n✅ Response:")
            print("-" * 40)
            print(response)
            print("-" * 40)
            print(f"✨ Success! Graph returned {len(result['messages'])} messages")
        else:
            print("❌ No response received")

    except Exception as e:
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    print("🧪 Testing LangGraph with fixed parameters\n")
    asyncio.run(test_langgraph_simple())
    print("\n🎉 Test completed!")
