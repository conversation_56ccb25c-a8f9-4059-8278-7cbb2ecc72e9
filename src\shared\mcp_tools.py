"""MCP tools integration for CatchUp customer service system."""

from __future__ import annotations

import asyncio
from typing import List, Optional
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import load_mcp_tools
from mcp import ClientSession
from mcp.client.sse import sse_client


class MCPToolsManager:
    """Manager for MCP tools integration."""
    
    def __init__(self, server_url: str):
        """Initialize MCP tools manager.
        
        Args:
            server_url: The URL of the MCP server (SSE endpoint)
        """
        self.server_url = server_url
        self._tools: Optional[List[BaseTool]] = None
        self._client: Optional[MultiServerMCPClient] = None
    
    async def initialize(self) -> None:
        """Initialize the MCP client and load tools."""
        try:
            # Create MultiServerMCPClient for SSE transport
            self._client = MultiServerMCPClient({
                "catchup": {
                    "transport": "sse",
                    "url": self.server_url,
                }
            })
            
            # Load tools from the MCP server
            self._tools = await self._client.get_tools()
            print(f"Successfully loaded {len(self._tools)} tools from CatchUpMCP server")
            
        except Exception as e:
            print(f"Failed to initialize MCP tools: {e}")
            self._tools = []
    
    async def get_tools(self) -> List[BaseTool]:
        """Get the loaded MCP tools.

        Returns:
            List of LangChain tools loaded from the MCP server
        """
        if self._tools is None:
            await self.initialize()

        return self._tools or []

    async def get_tools_by_names(self, tool_names: List[str]) -> List[BaseTool]:
        """Get specific tools by their names.

        Args:
            tool_names: List of tool names to retrieve

        Returns:
            List of LangChain tools that match the provided names
        """
        if self._tools is None:
            await self.initialize()

        available_tools = self._tools or []

        # Create a mapping of tool names to tools for efficient lookup
        tool_map = {tool.name: tool for tool in available_tools}

        # Find matching tools and track missing ones
        found_tools = []
        missing_tools = []

        for tool_name in tool_names:
            if tool_name in tool_map:
                found_tools.append(tool_map[tool_name])
            else:
                missing_tools.append(tool_name)

        # Print warning for missing tools
        if missing_tools:
            print(f"Warning: The following tools were not found in MCP: {missing_tools}")
            available_tool_names = [tool.name for tool in available_tools]
            print(f"Available tools: {available_tool_names}")

        return found_tools
    
    async def close(self) -> None:
        """Close the MCP client connection."""
        if self._client:
            # The MultiServerMCPClient doesn't have an explicit close method
            # but we can set it to None to allow garbage collection
            self._client = None
        self._tools = None


# Global instance for the MCP tools manager
_mcp_manager: Optional[MCPToolsManager] = None


async def get_mcp_tools_manager() -> MCPToolsManager:
    """Get or create the global MCP tools manager."""
    global _mcp_manager
    
    if _mcp_manager is None:
        _mcp_manager = MCPToolsManager("https://genenrativepangea.app.n8n.cloud/mcp/catchup/sse")
        await _mcp_manager.initialize()
    
    return _mcp_manager


async def get_catchup_tools() -> List[BaseTool]:
    """Get the CatchUp MCP tools.

    Returns:
        List of LangChain tools for the CatchUp customer service system
    """
    manager = await get_mcp_tools_manager()
    return await manager.get_tools()


async def get_catchup_tools_by_names(tool_names: List[str]) -> List[BaseTool]:
    """Get specific CatchUp MCP tools by their names.

    Args:
        tool_names: List of tool names to retrieve (e.g., ["tool1", "tool2", "tool3"])

    Returns:
        List of LangChain tools that match the provided names
    """
    manager = await get_mcp_tools_manager()
    return await manager.get_tools_by_names(tool_names)
