#!/usr/bin/env python3
"""Comprehensive test of LangGraph with multiple scenarios."""

import asyncio
import json
from langchain_core.messages import HumanMessage
from src.catchup.graph import graph


async def test_scenario(scenario_name: str, message: str, location: tuple = None, user_id: str = "test_user"):
    """Test a specific scenario with the LangGraph."""
    
    print(f"\n🎯 Testing Scenario: {scenario_name}")
    print("=" * 60)
    
    # Generate unique session for each test
    session_id = f"test_{scenario_name.lower().replace(' ', '_')}"
    
    # Configuration
    config = {
        "configurable": {
            "thread_id": session_id,
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful customer service assistant for a multi-tenant marketplace."
        }
    }
    
    # Prepare state
    state = {
        "messages": [HumanMessage(content=message)],
        "session_id": session_id,
        "user_id": user_id,
        "memory_lenght": "10"
    }
    
    # Add location if provided
    if location:
        state["latitude"] = str(location[0])
        state["longitude"] = str(location[1])
        print(f"📍 Location: lat={location[0]}, lng={location[1]}")
    
    print(f"💬 Input: {message}")
    print(f"👤 User: {user_id}")
    print("\n🔄 Processing...")
    
    try:
        # Invoke the graph
        result = await graph.ainvoke(state, config)
        
        if result and "messages" in result and result["messages"]:
            response = result["messages"][-1].content
            
            print("✅ Response:")
            print("-" * 40)
            print(response)
            print("-" * 40)
            
            # Try to parse as JSON if it looks like JSON
            if response.strip().startswith('{') and response.strip().endswith('}'):
                try:
                    parsed = json.loads(response)
                    print("\n📋 Parsed JSON Response:")
                    for key, value in parsed.items():
                        print(f"   {key}: {value}")
                except json.JSONDecodeError:
                    print("\n⚠️  Response looks like JSON but couldn't parse it")
            
            return result
        else:
            print("❌ No response received")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None


async def test_conversation_memory():
    """Test conversation memory across multiple messages."""
    
    print(f"\n🧠 Testing Conversation Memory")
    print("=" * 60)
    
    session_id = "memory_test_session"
    config = {
        "configurable": {
            "thread_id": session_id,
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful customer service assistant."
        }
    }
    
    # First message
    state1 = {
        "messages": [HumanMessage(content="My name is Alice and I'm looking for Italian restaurants")],
        "session_id": session_id,
        "user_id": "alice_123",
        "latitude": "45.4642",
        "longitude": "9.1900",
        "memory_lenght": "10"
    }
    
    print("💬 Message 1: My name is Alice and I'm looking for Italian restaurants")
    result1 = await graph.ainvoke(state1, config)
    if result1:
        print(f"🤖 Response 1: {result1['messages'][-1].content}")
    
    # Second message (should remember Alice)
    state2 = {
        "messages": [HumanMessage(content="What's my name?")],
        "session_id": session_id,
        "memory_lenght": "10"
    }
    
    print("\n💬 Message 2: What's my name?")
    result2 = await graph.ainvoke(state2, config)
    if result2:
        print(f"🤖 Response 2: {result2['messages'][-1].content}")
        print(f"📊 Total messages in conversation: {len(result2['messages'])}")


async def main():
    """Run comprehensive LangGraph tests."""
    
    print("🧪 LangGraph Comprehensive Test Suite")
    print("This will test various scenarios with the LangGraph implementation.")
    print("=" * 80)
    
    # Test scenarios
    scenarios = [
        {
            "name": "Restaurant Search",
            "message": "I'm looking for a good pizza place near me",
            "location": (45.4642, 9.1900),  # Milan
            "user_id": "pizza_lover_123"
        },
        {
            "name": "Booking Inquiry", 
            "message": "Can you help me check my booking status?",
            "location": (41.9028, 12.4964),  # Rome
            "user_id": "traveler_456"
        },
        {
            "name": "General Help",
            "message": "How does this marketplace work?",
            "user_id": "newbie_789"
        },
        {
            "name": "Deal Search",
            "message": "Show me the best deals available today",
            "location": (45.0703, 7.6869),  # Turin
            "user_id": "deal_hunter_101"
        }
    ]
    
    # Run each scenario
    for scenario in scenarios:
        await test_scenario(
            scenario["name"],
            scenario["message"],
            scenario.get("location"),
            scenario["user_id"]
        )
        
        # Small delay between tests
        await asyncio.sleep(1)
    
    # Test conversation memory
    await test_conversation_memory()
    
    print("\n" + "=" * 80)
    print("✨ All tests completed!")
    print("🎉 LangGraph is working correctly with various scenarios!")


if __name__ == "__main__":
    asyncio.run(main())
