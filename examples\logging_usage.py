"""
Examples of different ways to use the global logger.

This file demonstrates the various approaches to logging in your application.
"""

# ============================================================================
# Method 1: Import global logger directly (RECOMMENDED)
# ============================================================================
from shared.logger import logger

def example_with_global_logger():
    """Example using the global logger - simplest approach."""
    logger.info("This is using the global logger directly")
    logger.debug("Debug message from global logger")
    logger.warning("Warning message from global logger")
    logger.error("Error message from global logger")


# ============================================================================
# Method 2: Import from shared module
# ============================================================================
from shared import logger as shared_logger

def example_with_shared_import():
    """Example using logger imported from shared module."""
    shared_logger.info("This is using logger from shared module")
    shared_logger.debug("Debug message from shared import")


# ============================================================================
# Method 3: Traditional approach (still works)
# ============================================================================
from shared.logger import logger

traditional_

def example_with_traditional_logger():
    """Example using traditional logger approach."""
    traditional_logger.info("This is using traditional logger approach")
    traditional_logger.debug("Debug message from traditional logger")


# ============================================================================
# Method 4: Module-specific logger (for when you need module identification)
# ============================================================================
from shared.logger import logger

module_

def example_with_module_logger():
    """Example using module-specific logger for better traceability."""
    module_logger.info("This message will show the module name in logs")
    module_logger.debug("Debug message with module identification")


# ============================================================================
# Demonstration function
# ============================================================================
def demonstrate_all_logging_methods():
    """Demonstrate all logging methods."""
    print("=== Demonstrating Global Logger Usage ===")
    
    print("\n1. Global logger (recommended for most cases):")
    example_with_global_logger()
    
    print("\n2. Shared module import:")
    example_with_shared_import()
    
    print("\n3. Traditional approach:")
    example_with_traditional_logger()
    
    print("\n4. Module-specific logger:")
    example_with_module_logger()


if __name__ == "__main__":
    demonstrate_all_logging_methods()
